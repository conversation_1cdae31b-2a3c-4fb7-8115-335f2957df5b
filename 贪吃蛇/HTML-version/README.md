# 🐍 贪吃蛇游戏 Snake Game

一个基于HTML5 Canvas的经典贪吃蛇游戏，采用苹果风格的简约设计，适合初学者学习和娱乐。

## 📋 功能设计

### 核心功能
- **游戏画布**：使用HTML5 Canvas绘制游戏区域
- **蛇的移动**：键盘方向键控制蛇的移动方向
- **食物系统**：随机位置生成食物，蛇吃到食物后身体增长
- **碰撞检测**：检测蛇头撞墙或撞到自己身体的情况
- **分数系统**：吃食物获得分数，实时显示当前分数
- **游戏状态管理**：支持开始、暂停、结束、重新开始等状态

### 拓展功能
- **难度选择**：提供简单、普通、困难三种速度等级
- **最高分记录**：使用LocalStorage保存历史最高分
- **音效系统**：吃食物音效、游戏结束音效
- **主题切换**：多种颜色主题可选
- **移动端适配**：支持触摸屏操作，虚拟方向键
- **特殊食物**：不同类型食物提供不同分值

## 🎨 页面设计

### 设计风格
- **设计理念**：苹果风格简约设计，注重用户体验
- **视觉元素**：圆角矩形、柔和阴影、清晰层次
- **交互反馈**：按钮悬停效果、状态变化动画

### 颜色方案
```css
主色调：深绿色 #2D5A27  /* 蛇身颜色 */
辅助色：亮绿色 #4CAF50  /* 食物颜色 */
背景色：浅灰色 #F5F5F5  /* 页面背景 */
文字色：深灰色 #333333  /* 主要文字 */
强调色：橙色   #FF9800  /* 分数高亮 */
```

### 页面布局
```
┌─────────────────────────────────────┐
│           🐍 贪吃蛇游戏              │
│     分数: 0    最高分: 0    难度: 普通 │
├─────────────────────────────────────┤
│                                     │
│           游戏画布区域                │
│         (Canvas 600x400)            │
│                                     │
├─────────────────────────────────────┤
│  [开始] [暂停] [重置] [设置] [主题]   │
└─────────────────────────────────────┘
```

## 📁 目录结构

```
snake-game/
├── index.html              # 主页面入口
├── css/
│   ├── style.css          # 主样式文件
│   ├── themes.css         # 主题样式定义
│   └── responsive.css     # 响应式样式
├── js/
│   ├── game.js           # 游戏核心逻辑控制器
│   ├── snake.js          # 蛇类定义和行为
│   ├── food.js           # 食物类定义和生成
│   ├── ui.js             # 用户界面控制
│   ├── audio.js          # 音效管理
│   └── utils.js          # 工具函数库
├── assets/
│   ├── sounds/           # 音效文件目录
│   │   ├── eat.mp3      # 吃食物音效
│   │   └── gameover.mp3 # 游戏结束音效
│   └── images/           # 图片资源目录
│       └── icon.svg     # 游戏图标
├── README.md             # 项目说明文档
└── .gitignore           # Git忽略文件
```

## 🛠 技术框架

### 技术栈选择
- **前端技术**：HTML5 + CSS3 + 原生JavaScript
- **图形渲染**：HTML5 Canvas API
- **数据存储**：LocalStorage（本地存储）
- **音频处理**：HTML5 Audio API
- **响应式设计**：CSS Media Queries

### 技术选型理由
1. **简单易学**：适合初学者理解和学习
2. **无需构建**：直接在浏览器中运行
3. **兼容性好**：支持所有现代浏览器
4. **性能优秀**：Canvas渲染流畅
5. **易于部署**：静态文件，可直接部署到任何服务器

## 🎯 UI设计规范

### 组件设计
- **按钮样式**：圆角8px，渐变背景，悬停效果
- **面板样式**：白色背景，轻微阴影，圆角12px
- **文字规范**：主标题24px，副标题18px，正文16px
- **间距规范**：组件间距16px，内边距12px

### 响应式断点
```css
/* 桌面端 */
@media (min-width: 1024px) { /* 1200px居中布局 */ }

/* 平板端 */
@media (768px <= width < 1024px) { /* 适配平板 */ }

/* 手机端 */
@media (max-width: 767px) { /* 全屏布局 */ }
```

## 🚀 开发方案选型

### 开发环境
- **编辑器**：VS Code（推荐插件：Live Server）
- **浏览器**：Chrome/Safari（开发者工具调试）
- **版本控制**：Git
- **代码规范**：ESLint + Prettier

### 部署方案
- **开发环境**：Live Server本地预览
- **生产环境**：GitHub Pages / Netlify / Vercel

## 📅 开发优先级规划

### 第一阶段：核心功能（高优先级）
**预计时间：3-5天**
- [ ] 创建基础HTML结构和CSS样式
- [ ] 初始化Canvas画布和基础渲染
- [ ] 实现蛇的基本移动逻辑
- [ ] 添加键盘控制功能
- [ ] 实现食物生成和碰撞检测
- [ ] 完成基础分数系统
- [ ] 添加游戏结束判断逻辑

### 第二阶段：增强功能（中优先级）
**预计时间：2-3天**
- [ ] 完善游戏状态管理（开始/暂停/重置）
- [ ] 实现本地最高分存储功能
- [ ] 添加基础音效系统
- [ ] 优化用户界面和交互体验
- [ ] 添加游戏说明和帮助信息

### 第三阶段：拓展功能（低优先级）
**预计时间：3-4天**
- [ ] 实现难度选择功能
- [ ] 添加多主题切换
- [ ] 完成移动端适配和触摸控制
- [ ] 实现特殊食物类型
- [ ] 添加动画效果和视觉优化
- [ ] 性能优化和代码重构

## 🎮 业务逻辑设计

### 游戏流程
1. **初始化**：创建蛇（3节身体）和第一个食物
2. **游戏循环**：移动蛇 → 检测碰撞 → 更新画面 → 重复
3. **吃食物**：增加分数 → 蛇身增长 → 生成新食物
4. **游戏结束**：撞墙或撞自己 → 显示结果 → 保存最高分

### 核心算法
- **蛇移动**：队列数据结构，头部添加新位置，尾部移除
- **碰撞检测**：坐标比较算法
- **食物生成**：随机数生成，避免与蛇身重叠

---

## 🎯 项目目标

通过这个项目，你将学会：
- HTML5 Canvas基础绘图
- JavaScript面向对象编程
- 游戏循环和状态管理
- 事件处理和用户交互
- 本地数据存储
- 响应式网页设计

**让我们开始创造一个有趣的贪吃蛇游戏吧！** 🚀
