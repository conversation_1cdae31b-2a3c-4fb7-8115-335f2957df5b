/* CSS变量定义 - 基于设计系统 */
:root {
  /* 主色调 */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-dark: #5a6fd8;
  
  /* 功能色彩 */
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --error-color: #F44336;
  --info-color: #2196F3;
  
  /* 中性色彩 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-light: rgba(255,255,255,0.9);
  --background-white: #FFFFFF;
  --background-overlay: rgba(255,255,255,0.1);
  
  /* 字体系统 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  
  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-xxl: 24px;
  
  /* 圆角和阴影 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
  --shadow-md: 0 4px 15px rgba(0,0,0,0.1);
  --shadow-lg: 0 6px 20px rgba(0,0,0,0.15);
  --shadow-focus: 0 0 0 3px rgba(102, 126, 234, 0.3);
  
  /* 动画 */
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --ease-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 主体样式 */
body {
  width: 320px;
  font-family: var(--font-family);
  background: var(--primary-gradient);
  color: var(--text-primary);
  line-height: 1.4;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 主容器 */
.container {
  padding: var(--spacing-xl);
  animation: fadeIn var(--duration-normal) var(--ease-out);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 头部区域 */
.header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.header h2 {
  color: var(--text-light);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* 内容区域 */
.content {
  background: var(--background-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--spacing-lg);
}

/* 主要操作按钮 */
.capture-btn {
  width: 100%;
  min-height: 48px;
  padding: var(--spacing-md) var(--spacing-xl);
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-out);
  box-shadow: var(--shadow-md);
  
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  font-family: inherit;
  
  position: relative;
  overflow: hidden;
}

.capture-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.capture-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.capture-btn:focus {
  outline: none;
  box-shadow: var(--shadow-focus);
}

.capture-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 按钮图标和文字 */
.capture-btn .icon {
  font-size: 20px;
  line-height: 1;
}

.capture-btn .text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
}

/* 状态显示区域 */
.status {
  text-align: center;
  padding: var(--spacing-xl);
  background: var(--background-overlay);
  border-radius: var(--border-radius-lg);
  margin: var(--spacing-lg) 0;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  color: var(--text-light);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-md);
}

/* 加载动画 */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255,255,255,0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 进度条样式 */
.progress-container {
  width: 100%;
  height: 4px;
  background: rgba(255,255,255,0.2);
  border-radius: var(--border-radius-sm);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #45a049);
  border-radius: var(--border-radius-sm);
  transition: width var(--duration-normal) var(--ease-out);
  animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
  0% { opacity: 0.8; }
  50% { opacity: 1; }
  100% { opacity: 0.8; }
}

/* 结果显示区域 */
.result {
  text-align: center;
  padding: var(--spacing-xl);
  background: var(--background-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  animation: successSlideIn var(--duration-normal) var(--ease-out);
}

@keyframes successSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.success {
  color: var(--success-color);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

/* 预览容器 */
.preview-container {
  margin: var(--spacing-lg) 0;
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background: #f8f9fa;
  border-bottom: 1px solid var(--border-light);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.toggle-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  font-size: var(--font-size-xs);
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: background var(--duration-fast);
}

.toggle-btn:hover {
  background: rgba(102, 126, 234, 0.1);
}

.preview-area {
  max-height: 200px;
  overflow-y: auto;
  padding: var(--spacing-md);
  background: white;
}

.preview-image {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-sm);
  cursor: pointer;
  transition: transform var(--duration-fast);
}

.preview-image:hover {
  transform: scale(1.02);
}

/* 操作按钮容器 */
.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

/* 下载按钮 */
.download-btn {
  flex: 1;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--success-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  font-family: inherit;
  transition: all var(--duration-normal) var(--ease-out);
  box-shadow: var(--shadow-sm);

  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.download-btn:hover {
  background: #45a049;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.download-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.download-btn:focus {
  outline: none;
  box-shadow: var(--shadow-focus);
}

/* 复制按钮 */
.copy-btn {
  flex: 1;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--info-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  font-family: inherit;
  transition: all var(--duration-normal) var(--ease-out);
  box-shadow: var(--shadow-sm);

  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.copy-btn:hover {
  background: #1976d2;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.copy-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.copy-btn:focus {
  outline: none;
  box-shadow: var(--shadow-focus);
}

/* 错误状态样式 */
.error {
  color: var(--error-color);
  background: rgba(244, 67, 54, 0.1);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  border-left: 4px solid var(--error-color);
  font-size: var(--font-size-sm);
  margin: var(--spacing-md) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.error-icon {
  font-size: var(--font-size-lg);
}

.retry-btn {
  margin-left: auto;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--error-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: var(--font-size-xs);
  transition: background var(--duration-fast);
}

.retry-btn:hover {
  background: #d32f2f;
}

/* 页脚区域 */
.footer {
  text-align: center;
  margin-top: var(--spacing-lg);
  padding: 0 var(--spacing-lg);
}

.tip {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  opacity: 0.8;
  line-height: 1.4;
}

/* 工具类 */
.hidden {
  display: none !important;
}

.fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}

/* 响应式设计 */
@media (max-width: 360px) {
  body {
    width: 280px;
  }
  
  .container {
    padding: var(--spacing-lg);
  }
  
  .content {
    padding: var(--spacing-lg);
  }
  
  .capture-btn {
    min-height: 44px;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --background-white: #FFFFFF;
    --primary-color: #0000FF;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
