# 网页长图截取Chrome扩展

一键截取完整网页长图的Chrome浏览器扩展，专为保存不可复制的网页内容而设计。

## ✨ 功能特点

- 🖼️ **一键截图** - 点击即可截取完整网页长图
- 📱 **智能拼接** - 自动处理超长页面，无缝拼接多张截图
- ⚡ **快速响应** - 优化的截图算法，快速完成截图任务
- 🎯 **简单易用** - 极简界面设计，无需复杂设置
- 💾 **自动下载** - 截图完成后自动保存到本地
- 🔧 **兼容性强** - 支持各种类型的网页和动态内容

## 🚀 快速开始

### 安装方法

#### 方法一：开发者模式安装（推荐）
1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目中的 `webpage-screenshot-extension` 文件夹
6. 扩展安装完成！

#### 方法二：生成图标文件
1. 打开 `icons/create-icons.html` 文件
2. 点击"下载所有图标"按钮
3. 将下载的图标文件放入 `icons` 文件夹
4. 按照方法一安装扩展

### 使用方法

1. **打开目标网页** - 访问你想要截图的网页
2. **点击扩展图标** - 在浏览器工具栏中找到扩展图标并点击
3. **开始截图** - 在弹出的界面中点击"截取完整页面"按钮
4. **等待完成** - 扩展会自动滚动页面并截取多张图片
5. **下载图片** - 截图完成后点击"下载图片"按钮保存到本地

## 🛠️ 技术架构

### 核心组件
- **manifest.json** - 扩展配置文件
- **popup/** - 用户界面（HTML + CSS + JS）
- **content/** - 页面内容脚本
- **background/** - 后台服务脚本
- **icons/** - 扩展图标资源

### 技术栈
- HTML5 + CSS3 + JavaScript (ES6+)
- Chrome Extension API
- Canvas API (图片处理)
- 现代CSS设计系统

### 工作原理
1. **页面分析** - 获取网页的实际尺寸
2. **分段截图** - 按视窗高度分段滚动截图
3. **图片拼接** - 使用Canvas API拼接所有截图
4. **文件下载** - 生成PNG格式图片并自动下载

## 📋 支持的页面类型

### ✅ 支持的页面
- HTTP/HTTPS网页
- 静态网页内容
- 动态加载的内容
- 响应式设计页面
- 包含图片的页面
- 长页面和短页面

### ❌ 不支持的页面
- Chrome内部页面 (chrome://)
- 扩展管理页面
- 新标签页
- 文件协议页面 (file://)

## 🔧 开发指南

### 项目结构
```
webpage-screenshot-extension/
├── manifest.json          # 扩展配置
├── popup/                 # 弹窗界面
│   ├── popup.html        # 界面结构
│   ├── popup.css         # 样式设计
│   └── popup.js          # 交互逻辑
├── content/              # 内容脚本
│   └── content.js        # 页面注入脚本
├── background/           # 后台脚本
│   └── background.js     # 后台服务
├── icons/                # 图标资源
│   ├── create-icons.html # 图标生成器
│   ├── icon16.png        # 16x16图标
│   ├── icon48.png        # 48x48图标
│   └── icon128.png       # 128x128图标
└── README.md             # 说明文档
```

### 本地开发
1. 修改代码后，在扩展管理页面点击"重新加载"
2. 使用Chrome开发者工具调试
3. 查看控制台输出了解运行状态

### 调试方法
- **Popup调试**: 右键扩展图标 → "检查弹出内容"
- **Content Script调试**: 在目标页面按F12打开开发者工具
- **Background调试**: 扩展管理页面 → "背景页面" → "检查视图"

## 🐛 常见问题

### Q: 截图失败怎么办？
A: 请检查：
- 是否在支持的页面类型上使用
- 网页是否完全加载完成
- 是否有网络连接问题

### Q: 截图不完整怎么办？
A: 可能原因：
- 页面有懒加载内容，等待页面完全加载后再截图
- 页面有动态内容，稍等片刻再尝试

### Q: 下载的图片太大怎么办？
A: 这是正常现象，完整页面截图文件会比较大。可以：
- 使用图片压缩工具减小文件大小
- 考虑截取页面的特定部分

### Q: 在某些网站无法使用？
A: 部分网站可能有特殊的安全策略，建议：
- 刷新页面后重试
- 检查网站是否阻止了扩展权限

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 首次发布
- 🖼️ 基础截图功能
- 🎨 现代化UI设计
- 📱 响应式界面
- 🔧 完善的错误处理

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork此项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- Chrome Extension API 文档
- 现代CSS设计系统
- 开源社区的贡献

---

**如果这个扩展对你有帮助，请给个⭐️支持一下！**
