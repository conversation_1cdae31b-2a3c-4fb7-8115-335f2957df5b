<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>生成扩展图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .icon-preview {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ddd;
            margin: 10px 0;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <h1>网页截图扩展图标生成器</h1>
    <p>点击下载按钮生成不同尺寸的图标文件</p>
    
    <div class="icon-container">
        <div class="icon-preview">
            <h3>16x16</h3>
            <canvas id="icon16" width="16" height="16"></canvas>
            <br>
            <button onclick="downloadIcon('icon16', 'icon16.png')">下载 16x16</button>
        </div>
        
        <div class="icon-preview">
            <h3>48x48</h3>
            <canvas id="icon48" width="48" height="48"></canvas>
            <br>
            <button onclick="downloadIcon('icon48', 'icon48.png')">下载 48x48</button>
        </div>
        
        <div class="icon-preview">
            <h3>128x128</h3>
            <canvas id="icon128" width="128" height="128"></canvas>
            <br>
            <button onclick="downloadIcon('icon128', 'icon128.png')">下载 128x128</button>
        </div>
    </div>
    
    <button onclick="downloadAllIcons()">下载所有图标</button>
    
    <script>
        // 绘制图标的函数
        function drawIcon(canvas) {
            const ctx = canvas.getContext('2d');
            const size = canvas.width;
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制圆角矩形背景
            const radius = size * 0.15;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            
            // 绘制相机图标
            ctx.fillStyle = 'white';
            
            // 相机主体
            const cameraWidth = size * 0.6;
            const cameraHeight = size * 0.45;
            const cameraX = (size - cameraWidth) / 2;
            const cameraY = size * 0.35;
            
            ctx.fillRect(cameraX, cameraY, cameraWidth, cameraHeight);
            
            // 镜头
            const lensRadius = size * 0.15;
            const lensX = size / 2;
            const lensY = cameraY + cameraHeight / 2;
            
            ctx.fillStyle = '#333';
            ctx.beginPath();
            ctx.arc(lensX, lensY, lensRadius, 0, 2 * Math.PI);
            ctx.fill();
            
            // 镜头内圈
            ctx.fillStyle = '#666';
            ctx.beginPath();
            ctx.arc(lensX, lensY, lensRadius * 0.6, 0, 2 * Math.PI);
            ctx.fill();
            
            // 闪光灯
            const flashSize = size * 0.08;
            ctx.fillStyle = 'white';
            ctx.fillRect(cameraX + cameraWidth * 0.75, cameraY + flashSize, flashSize, flashSize);
            
            // 取景器
            const viewfinderWidth = size * 0.2;
            const viewfinderHeight = size * 0.1;
            ctx.fillRect((size - viewfinderWidth) / 2, cameraY - viewfinderHeight, viewfinderWidth, viewfinderHeight);
        }
        
        // Canvas roundRect polyfill for older browsers
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
        
        // 下载图标
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // 下载所有图标
        function downloadAllIcons() {
            downloadIcon('icon16', 'icon16.png');
            setTimeout(() => downloadIcon('icon48', 'icon48.png'), 100);
            setTimeout(() => downloadIcon('icon128', 'icon128.png'), 200);
        }
        
        // 初始化绘制所有图标
        window.onload = function() {
            drawIcon(document.getElementById('icon16'));
            drawIcon(document.getElementById('icon48'));
            drawIcon(document.getElementById('icon128'));
        };
    </script>
</body>
</html>
