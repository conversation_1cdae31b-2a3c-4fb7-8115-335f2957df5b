<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网页截图扩展测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .section {
            padding: 80px 0;
            border-bottom: 1px solid #eee;
        }
        
        .section:nth-child(even) {
            background: #f8f9fa;
        }
        
        .section h2 {
            font-size: 2.5rem;
            margin-bottom: 30px;
            text-align: center;
            color: #2c3e50;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        .card {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }
        
        .card p {
            color: #666;
            line-height: 1.8;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list .icon {
            font-size: 1.5rem;
            margin-right: 15px;
            width: 40px;
            text-align: center;
        }
        
        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .image-placeholder {
            height: 200px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 1.1rem;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        
        .stat-item {
            text-align: center;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: #667eea;
            display: block;
        }
        
        .stat-label {
            color: #666;
            margin-top: 10px;
        }
        
        .long-content {
            background: white;
            padding: 40px;
            border-radius: 12px;
            margin: 40px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .long-content h3 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        
        .long-content p {
            margin-bottom: 20px;
            text-align: justify;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 60px 20px;
            text-align: center;
        }
        
        .footer h3 {
            margin-bottom: 20px;
        }
        
        .footer p {
            opacity: 0.8;
        }
        
        .scroll-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(102, 126, 234, 0.3);
            z-index: 1000;
        }
        
        .scroll-progress {
            height: 100%;
            background: #667eea;
            width: 0%;
            transition: width 0.1s ease;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .section h2 {
                font-size: 2rem;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="scroll-indicator">
        <div class="scroll-progress" id="scrollProgress"></div>
    </div>
    
    <header class="header">
        <div class="container">
            <h1>🖼️ 网页截图扩展测试页面</h1>
            <p>这是一个用于测试网页长图截取功能的演示页面</p>
        </div>
    </header>
    
    <section class="section">
        <div class="container">
            <h2>📸 功能特点</h2>
            <div class="grid">
                <div class="card">
                    <h3>🚀 一键截图</h3>
                    <p>点击扩展图标，一键截取完整网页长图，无需复杂操作，简单高效。</p>
                </div>
                <div class="card">
                    <h3>🧩 智能拼接</h3>
                    <p>自动处理超长页面，智能分段截图并无缝拼接，确保内容完整性。</p>
                </div>
                <div class="card">
                    <h3>⚡ 快速响应</h3>
                    <p>优化的截图算法，快速完成截图任务，节省您的宝贵时间。</p>
                </div>
                <div class="card">
                    <h3>🎯 简单易用</h3>
                    <p>极简界面设计，无需复杂设置，技术小白也能轻松上手使用。</p>
                </div>
            </div>
        </div>
    </section>
    
    <section class="section">
        <div class="container">
            <h2>📊 使用统计</h2>
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">10,000+</span>
                    <div class="stat-label">用户下载</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">50,000+</span>
                    <div class="stat-label">截图次数</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">99.9%</span>
                    <div class="stat-label">成功率</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">4.8/5</span>
                    <div class="stat-label">用户评分</div>
                </div>
            </div>
        </div>
    </section>
    
    <section class="section">
        <div class="container">
            <h2>🎨 图片展示</h2>
            <div class="image-gallery">
                <div class="image-placeholder">示例图片 1</div>
                <div class="image-placeholder">示例图片 2</div>
                <div class="image-placeholder">示例图片 3</div>
                <div class="image-placeholder">示例图片 4</div>
                <div class="image-placeholder">示例图片 5</div>
                <div class="image-placeholder">示例图片 6</div>
            </div>
        </div>
    </section>
    
    <section class="section">
        <div class="container">
            <h2>✨ 支持功能</h2>
            <ul class="feature-list">
                <li>
                    <span class="icon">🖼️</span>
                    <div>
                        <strong>完整页面截图</strong> - 支持截取整个网页内容，包括超长页面
                    </div>
                </li>
                <li>
                    <span class="icon">📱</span>
                    <div>
                        <strong>响应式页面支持</strong> - 完美适配各种屏幕尺寸和设备
                    </div>
                </li>
                <li>
                    <span class="icon">🔧</span>
                    <div>
                        <strong>动态内容处理</strong> - 智能等待页面加载，确保动态内容完整
                    </div>
                </li>
                <li>
                    <span class="icon">💾</span>
                    <div>
                        <strong>自动保存下载</strong> - 截图完成后自动保存到本地下载文件夹
                    </div>
                </li>
                <li>
                    <span class="icon">⚡</span>
                    <div>
                        <strong>高性能处理</strong> - 优化的算法确保快速完成截图任务
                    </div>
                </li>
                <li>
                    <span class="icon">🎯</span>
                    <div>
                        <strong>精确拼接</strong> - 像素级精确拼接，确保图片质量
                    </div>
                </li>
            </ul>
        </div>
    </section>
    
    <section class="section">
        <div class="container">
            <h2>📖 详细说明</h2>
            <div class="long-content">
                <h3>关于网页截图扩展</h3>
                <p>网页截图扩展是一款专为Chrome浏览器设计的强大工具，旨在帮助用户快速、准确地截取完整的网页长图。无论是学习笔记、工作资料还是设计参考，这款扩展都能满足您的需求。</p>
                
                <p>传统的截图工具往往只能截取当前可见区域，对于长页面内容无能为力。我们的扩展采用先进的分段截图技术，能够自动滚动页面并截取多张图片，然后使用智能算法将这些图片无缝拼接成一张完整的长图。</p>
                
                <p>扩展的设计理念是简单易用。我们相信，强大的功能不应该以复杂的操作为代价。因此，整个截图过程只需要一次点击即可完成，即使是技术小白也能轻松上手。</p>
                
                <p>在技术实现上，我们采用了最新的Chrome Extension API，确保扩展的稳定性和兼容性。同时，我们还对性能进行了深度优化，即使是超长页面也能在短时间内完成截图。</p>
                
                <p>用户体验是我们最关注的方面。扩展界面采用现代化的设计语言，配色和动画都经过精心设计，为用户提供愉悦的使用体验。我们还加入了详细的状态提示和错误处理机制，确保用户在任何情况下都能获得清晰的反馈。</p>
                
                <p>隐私保护也是我们的重要考量。扩展完全在本地运行，不会上传任何用户数据到服务器，确保您的隐私安全。所有截图都保存在本地，您可以完全控制这些文件的使用和分享。</p>
                
                <p>我们持续关注用户反馈，不断改进和优化扩展功能。如果您在使用过程中遇到任何问题或有任何建议，欢迎随时联系我们。我们致力于为用户提供最好的网页截图解决方案。</p>
            </div>
        </div>
    </section>
    
    <footer class="footer">
        <div class="container">
            <h3>🙏 感谢使用</h3>
            <p>网页截图扩展 - 让网页内容保存变得简单高效</p>
            <p>如果这个扩展对您有帮助，请给我们一个好评！</p>
        </div>
    </footer>
    
    <script>
        // 滚动进度指示器
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            document.getElementById('scrollProgress').style.width = scrollPercent + '%';
        });
        
        // 添加一些动态内容来测试扩展
        setTimeout(function() {
            const dynamicContent = document.createElement('div');
            dynamicContent.innerHTML = '<p style="text-align: center; padding: 20px; background: #e8f4fd; margin: 20px 0; border-radius: 8px;">✨ 这是动态加载的内容，用于测试扩展对动态内容的处理能力</p>';
            document.querySelector('.long-content').appendChild(dynamicContent);
        }, 2000);
        
        console.log('测试页面已加载，可以开始测试网页截图扩展功能');
    </script>
</body>
</html>
