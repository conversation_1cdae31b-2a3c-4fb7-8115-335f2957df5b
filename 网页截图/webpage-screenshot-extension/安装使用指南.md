# 网页截图扩展 - 安装使用指南

## 📦 安装步骤

### 第一步：准备图标文件
1. 打开 `icons/create-icons.html` 文件（双击或在浏览器中打开）
2. 点击"下载所有图标"按钮
3. 将下载的三个图标文件（icon16.png, icon48.png, icon128.png）放入 `icons` 文件夹

### 第二步：安装扩展
1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/` 并回车
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"按钮
5. 选择 `webpage-screenshot-extension` 文件夹
6. 扩展安装完成！

### 第三步：验证安装
- 在浏览器工具栏中应该能看到扩展图标（相机图标）
- 点击图标应该能弹出扩展界面

## 🚀 使用方法

### 基本使用
1. **打开目标网页** - 访问你想要截图的网页
2. **点击扩展图标** - 在浏览器工具栏中点击相机图标
3. **开始截图** - 在弹出界面中点击"截取完整页面"按钮
4. **等待完成** - 扩展会自动处理，显示进度
5. **下载图片** - 截图完成后点击"下载图片"按钮

### 快捷键
- `Ctrl/Cmd + Enter` - 在弹窗中快速开始截图
- `Escape` - 关闭扩展弹窗

### 测试功能
1. 打开 `test-page.html` 文件测试扩展功能
2. 这是一个专门设计的长页面，包含各种元素
3. 可以验证扩展的截图和拼接效果

## ⚙️ 功能说明

### 支持的页面类型
✅ **支持**
- HTTP/HTTPS网页
- 静态和动态内容
- 响应式设计页面
- 包含图片的页面
- 超长页面

❌ **不支持**
- Chrome内部页面 (chrome://)
- 扩展管理页面
- 新标签页
- 本地文件 (file://)

### 截图过程
1. **页面分析** - 自动检测页面尺寸
2. **智能滚动** - 按视窗高度分段滚动
3. **等待加载** - 确保动态内容完全加载
4. **分段截图** - 截取每个可见区域
5. **无缝拼接** - 使用Canvas API拼接图片
6. **自动下载** - 生成PNG文件并下载

## 🔧 故障排除

### 常见问题

#### 问题1：扩展图标不显示
**解决方案：**
- 确保已正确安装扩展
- 检查是否开启了开发者模式
- 尝试重新加载扩展

#### 问题2：点击图标没有反应
**解决方案：**
- 检查当前页面是否为支持的页面类型
- 刷新页面后重试
- 查看浏览器控制台是否有错误信息

#### 问题3：截图失败或不完整
**解决方案：**
- 等待页面完全加载后再截图
- 检查网络连接是否正常
- 对于动态内容较多的页面，稍等片刻再尝试

#### 问题4：下载的图片太大
**解决方案：**
- 这是正常现象，完整页面截图文件会比较大
- 可以使用图片压缩工具减小文件大小
- 考虑只截取页面的特定部分

#### 问题5：某些网站无法使用
**解决方案：**
- 部分网站可能有安全策略限制
- 尝试刷新页面后重试
- 检查网站是否阻止了扩展权限

### 调试方法

#### 查看扩展日志
1. **Popup调试**
   - 右键扩展图标 → "检查弹出内容"
   - 查看Console标签页的日志信息

2. **Content Script调试**
   - 在目标页面按F12打开开发者工具
   - 查看Console标签页的日志信息

3. **Background调试**
   - 访问 `chrome://extensions/`
   - 找到扩展，点击"背景页面"下的"检查视图"
   - 查看Console标签页的日志信息

#### 重新加载扩展
1. 访问 `chrome://extensions/`
2. 找到"网页长图截取工具"扩展
3. 点击刷新图标重新加载扩展

## 📋 技术要求

### 浏览器要求
- Chrome 88+ 或基于Chromium的浏览器
- 开启JavaScript支持
- 允许扩展权限

### 系统要求
- Windows 7+, macOS 10.12+, 或 Linux
- 至少2GB可用内存
- 足够的磁盘空间存储截图文件

### 权限说明
扩展需要以下权限：
- `activeTab` - 访问当前活动标签页
- `scripting` - 注入内容脚本
- `downloads` - 下载截图文件

## 🔄 更新和维护

### 检查更新
- 扩展会自动检查更新
- 也可以手动重新安装最新版本

### 卸载扩展
1. 访问 `chrome://extensions/`
2. 找到扩展，点击"移除"按钮
3. 确认卸载

### 数据清理
- 扩展不会在浏览器中存储个人数据
- 截图文件保存在本地下载文件夹
- 卸载扩展不会删除已下载的截图文件

## 📞 获取帮助

### 反馈渠道
- 查看项目README文档
- 提交GitHub Issue
- 发送邮件反馈

### 常用资源
- [Chrome扩展开发文档](https://developer.chrome.com/docs/extensions/)
- [项目源代码](https://github.com/your-repo)
- [使用教程视频](https://example.com/tutorial)

---

**祝您使用愉快！如果扩展对您有帮助，请给我们一个好评！⭐**
