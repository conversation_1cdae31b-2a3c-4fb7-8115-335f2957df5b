# AI生图工具使用说明

## 快速开始

### 1. 配置API密钥
1. 点击右上角的"设置"按钮
2. 在弹出的设置窗口中输入您的硅基流动API密钥
3. 点击"保存"按钮

> **获取API密钥**: 请访问 [硅基流动控制台](https://cloud.siliconflow.cn/account/ak) 获取您的API密钥

### 2. 生成图片
1. 在"提示词"输入框中描述您想要生成的图片
2. 根据需要调整参数设置
3. 点击"生成图片"按钮
4. 等待生成完成

### 3. 下载图片
1. 生成完成后，将鼠标悬停在图片上
2. 点击右上角的下载按钮
3. 图片将自动下载到您的设备

## 参数说明

### 提示词 (Prompt)
- **作用**: 描述您想要生成的图片内容
- **建议**: 使用具体、详细的描述，包含风格、颜色、构图等信息
- **示例**: "一只可爱的橘色小猫在阳光明媚的花园里玩耍，水彩画风格"

### 负面提示词 (Negative Prompt)
- **作用**: 描述您不希望在图片中出现的内容
- **建议**: 用于排除低质量、模糊、变形等不良效果
- **示例**: "模糊、低质量、变形、噪点"

### 图片尺寸
- **1024x1024**: 正方形，适合头像、图标等
- **960x1280**: 3:4竖屏，适合手机壁纸
- **768x1024**: 3:4竖屏，较小尺寸
- **720x1440**: 1:2长竖屏，适合长图
- **720x1280**: 9:16手机竖屏，适合短视频封面

### 生成数量
- **范围**: 1-4张
- **说明**: 一次可以生成多张不同的图片
- **注意**: 生成数量越多，消耗的API配额越多

### 推理步数
- **范围**: 1-100步
- **默认**: 20步
- **说明**: 步数越多，图片质量越高，但生成时间越长
- **建议**: 一般情况下20-50步即可获得良好效果

### 引导系数
- **范围**: 0-20
- **默认**: 7.5
- **说明**: 控制生成结果与提示词的匹配度
- **建议**: 
  - 7-10: 平衡创意和准确性
  - 10-15: 更严格遵循提示词
  - 5-7: 更多创意发挥

### 随机种子
- **作用**: 控制随机性，相同种子会生成相似图片
- **用法**: 
  - 留空: 完全随机
  - 输入数字: 可重现的结果
  - 点击骰子按钮: 生成随机种子

## 提示词编写技巧

### 基本结构
```
[主体] + [动作/状态] + [环境/背景] + [风格] + [质量词]
```

### 示例
- **人物**: "一位美丽的女性，长发飘逸，微笑着，站在海边，日落时分，写实摄影风格，高清"
- **风景**: "雪山湖泊，倒影清晰，蓝天白云，阳光透过云层，风景摄影，4K高清"
- **动物**: "一只威武的老虎，在丛林中行走，阳光斑驳，野生动物摄影，专业摄影"
- **建筑**: "现代摩天大楼，玻璃幕墙，夜景灯光，城市天际线，建筑摄影，超高清"

### 风格关键词
- **摄影风格**: 人像摄影、风景摄影、街拍、微距摄影
- **艺术风格**: 油画、水彩、素描、版画、抽象艺术
- **动画风格**: 动漫、卡通、3D渲染、像素艺术
- **时代风格**: 复古、现代、未来主义、蒸汽朋克

### 质量提升词
- 高清、4K、8K、超高清
- 专业摄影、获奖作品
- 精细细节、锐利清晰
- 完美构图、专业打光

## 常见问题

### Q: 为什么生成失败？
A: 可能的原因：
- API密钥未配置或无效
- 网络连接问题
- 提示词包含敏感内容
- API配额不足

### Q: 如何提高生成质量？
A: 建议：
- 使用详细、具体的提示词
- 适当增加推理步数
- 使用负面提示词排除不良效果
- 尝试不同的引导系数值

### Q: 图片无法下载怎么办？
A: 解决方法：
- 检查浏览器下载权限
- 尝试右键保存图片
- 复制图片链接在新标签页打开

### Q: 如何重现之前的生成结果？
A: 方法：
- 使用相同的提示词和参数
- 设置相同的随机种子
- 注意API可能会有更新，结果可能略有差异

## 快捷键

- **Ctrl/Cmd + Enter**: 快速生成图片
- **Escape**: 关闭弹窗

## 注意事项

1. **图片有效期**: 生成的图片链接有效期为1小时，请及时下载保存
2. **API配额**: 每次生成都会消耗API配额，请合理使用
3. **内容政策**: 请遵守硅基流动的内容政策，不要生成违规内容
4. **网络要求**: 建议使用稳定的网络连接，生成过程需要联网
5. **浏览器兼容**: 建议使用现代浏览器（Chrome、Firefox、Safari、Edge）

## 技术支持

如果您在使用过程中遇到问题，可以：
1. 查看浏览器控制台的错误信息
2. 检查网络连接状态
3. 确认API密钥是否正确
4. 尝试刷新页面重新开始

---

*最后更新: 2024年*
