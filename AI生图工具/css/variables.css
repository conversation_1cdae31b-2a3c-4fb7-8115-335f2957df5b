/* CSS变量定义 */
:root {
  /* 主色调 */
  --primary-color: #6366f1;          /* 靛蓝色 - 主要按钮、链接 */
  --primary-light: #a5b4fc;          /* 浅靛蓝 - 悬停状态 */
  --primary-dark: #4338ca;           /* 深靛蓝 - 激活状态 */

  /* 辅助色 */
  --secondary-color: #64748b;        /* 灰蓝色 - 次要文本 */
  --accent-color: #f59e0b;           /* 琥珀色 - 强调元素 */

  /* 状态色 */
  --success-color: #10b981;          /* 成功状态 */
  --warning-color: #f59e0b;          /* 警告状态 */
  --error-color: #ef4444;            /* 错误状态 */
  --info-color: #3b82f6;             /* 信息状态 */

  /* 中性色 */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* 背景色 */
  --bg-primary: #ffffff;             /* 主背景 */
  --bg-secondary: #f8fafc;           /* 次要背景 */
  --bg-tertiary: #f1f5f9;            /* 第三级背景 */

  /* 字体族 */
  --font-family-base: 'Inter', 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

  /* 字体大小 */
  --font-size-xs: 0.75rem;           /* 12px */
  --font-size-sm: 0.875rem;          /* 14px */
  --font-size-base: 1rem;            /* 16px */
  --font-size-lg: 1.125rem;          /* 18px */
  --font-size-xl: 1.25rem;           /* 20px */
  --font-size-2xl: 1.5rem;           /* 24px */
  --font-size-3xl: 1.875rem;         /* 30px */
  --font-size-4xl: 2.25rem;          /* 36px */

  /* 字重 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 间距系统 (基于 4px) */
  --spacing-1: 0.25rem;              /* 4px */
  --spacing-2: 0.5rem;               /* 8px */
  --spacing-3: 0.75rem;              /* 12px */
  --spacing-4: 1rem;                 /* 16px */
  --spacing-5: 1.25rem;              /* 20px */
  --spacing-6: 1.5rem;               /* 24px */
  --spacing-8: 2rem;                 /* 32px */
  --spacing-10: 2.5rem;              /* 40px */
  --spacing-12: 3rem;                /* 48px */
  --spacing-16: 4rem;                /* 64px */

  /* 圆角 */
  --border-radius-sm: 0.375rem;      /* 6px */
  --border-radius-base: 0.5rem;      /* 8px */
  --border-radius-lg: 0.75rem;       /* 12px */
  --border-radius-xl: 1rem;          /* 16px */

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-base: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* 布局尺寸 */
  --header-height: 4rem;             /* 64px */
  --sidebar-width: 20rem;            /* 320px */
  --container-max-width: 1200px;
  
  /* 响应式断点 */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
}

/* 暗色主题变量 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    
    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-400: #64748b;
    --gray-500: #94a3b8;
    --gray-600: #cbd5e1;
    --gray-700: #e2e8f0;
    --gray-800: #f1f5f9;
    --gray-900: #f8fafc;
  }
}
