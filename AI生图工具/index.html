<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI生图工具 - 硅基流动</title>
    <link rel="stylesheet" href="css/variables.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-magic"></i>
                <span>AI生图工具</span>
            </div>
            <nav class="nav">
                <button class="nav-btn" id="settingsBtn">
                    <i class="fas fa-cog"></i>
                    <span>设置</span>
                </button>
                <button class="nav-btn" id="helpBtn">
                    <i class="fas fa-question-circle"></i>
                    <span>帮助</span>
                </button>
            </nav>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main">
        <!-- 参数设置面板 -->
        <aside class="parameter-panel">
            <div class="panel-header">
                <h2>参数设置</h2>
            </div>
            
            <div class="panel-content">
                <!-- 提示词输入 -->
                <div class="form-group">
                    <label for="promptInput">提示词</label>
                    <textarea 
                        id="promptInput" 
                        class="form-control" 
                        placeholder="请输入图片描述，例如：一只可爱的小猫在花园里玩耍"
                        rows="3"
                    ></textarea>
                </div>

                <!-- 负面提示词 -->
                <div class="form-group">
                    <label for="negativePromptInput">负面提示词 (可选)</label>
                    <textarea 
                        id="negativePromptInput" 
                        class="form-control" 
                        placeholder="不希望出现的内容，例如：模糊、低质量、变形"
                        rows="2"
                    ></textarea>
                </div>

                <!-- 图片尺寸 -->
                <div class="form-group">
                    <label for="imageSizeSelect">图片尺寸</label>
                    <select id="imageSizeSelect" class="form-control">
                        <option value="1024x1024">1024x1024 (1:1 正方形)</option>
                        <option value="960x1280">960x1280 (3:4 竖屏)</option>
                        <option value="768x1024">768x1024 (3:4 竖屏)</option>
                        <option value="720x1440">720x1440 (1:2 长竖屏)</option>
                        <option value="720x1280">720x1280 (9:16 手机竖屏)</option>
                    </select>
                </div>

                <!-- 生成数量 -->
                <div class="form-group">
                    <label for="batchSizeInput">生成数量</label>
                    <input 
                        type="number" 
                        id="batchSizeInput" 
                        class="form-control" 
                        min="1" 
                        max="4" 
                        value="1"
                    >
                </div>

                <!-- 推理步数 -->
                <div class="form-group">
                    <label for="stepsInput">推理步数</label>
                    <div class="slider-group">
                        <input 
                            type="range" 
                            id="stepsInput" 
                            class="form-slider" 
                            min="1" 
                            max="100" 
                            value="20"
                        >
                        <span class="slider-value" id="stepsValue">20</span>
                    </div>
                </div>

                <!-- 引导系数 -->
                <div class="form-group">
                    <label for="guidanceInput">引导系数</label>
                    <div class="slider-group">
                        <input 
                            type="range" 
                            id="guidanceInput" 
                            class="form-slider" 
                            min="0" 
                            max="20" 
                            step="0.5" 
                            value="7.5"
                        >
                        <span class="slider-value" id="guidanceValue">7.5</span>
                    </div>
                </div>

                <!-- 随机种子 -->
                <div class="form-group">
                    <label for="seedInput">随机种子 (可选)</label>
                    <div class="seed-group">
                        <input 
                            type="number" 
                            id="seedInput" 
                            class="form-control" 
                            placeholder="留空为随机"
                            min="0"
                            max="9999999999"
                        >
                        <button type="button" class="btn-secondary" id="randomSeedBtn">
                            <i class="fas fa-dice"></i>
                        </button>
                    </div>
                </div>

                <!-- 生成按钮 -->
                <button class="btn-primary btn-generate" id="generateBtn">
                    <i class="fas fa-magic"></i>
                    <span>生成图片</span>
                </button>
            </div>
        </aside>

        <!-- 图片生成区域 -->
        <section class="generation-area">
            <div class="generation-header">
                <h2>生成结果</h2>
            </div>
            
            <div class="generation-content" id="generationContent">
                <!-- 默认状态 -->
                <div class="empty-state" id="emptyState">
                    <div class="empty-icon">
                        <i class="fas fa-image"></i>
                    </div>
                    <h3>开始创作</h3>
                    <p>输入提示词，点击生成按钮开始创作您的AI艺术作品</p>
                </div>

                <!-- 加载状态 -->
                <div class="loading-state hidden" id="loadingState">
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                    </div>
                    <h3>正在生成中...</h3>
                    <p>请稍候，AI正在为您创作精美的图片</p>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </div>

                <!-- 结果展示 -->
                <div class="result-grid hidden" id="resultGrid">
                    <!-- 动态生成的图片卡片将插入这里 -->
                </div>
            </div>
        </section>
    </main>

    <!-- 设置弹窗 -->
    <div class="modal hidden" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>设置</h3>
                <button class="modal-close" id="closeSettingsModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="apiKeyInput">硅基流动 API Key</label>
                    <input 
                        type="password" 
                        id="apiKeyInput" 
                        class="form-control" 
                        placeholder="请输入您的API密钥"
                    >
                    <small class="form-help">
                        请在 <a href="https://cloud.siliconflow.cn/account/ak" target="_blank">硅基流动控制台</a> 获取API密钥
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" id="cancelSettings">取消</button>
                <button class="btn-primary" id="saveSettings">保存</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div class="modal hidden" id="helpModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>使用帮助</h3>
                <button class="modal-close" id="closeHelpModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="help-content">
                    <h4>如何使用</h4>
                    <ol>
                        <li>首先在设置中配置您的硅基流动API密钥</li>
                        <li>在提示词输入框中描述您想要生成的图片</li>
                        <li>调整参数设置（可选）</li>
                        <li>点击"生成图片"按钮</li>
                        <li>等待生成完成，然后可以下载图片</li>
                    </ol>
                    
                    <h4>提示词建议</h4>
                    <ul>
                        <li>使用具体、详细的描述</li>
                        <li>包含风格、颜色、构图等信息</li>
                        <li>可以参考艺术家风格或摄影技法</li>
                        <li>使用负面提示词排除不想要的内容</li>
                    </ul>
                    
                    <h4>参数说明</h4>
                    <ul>
                        <li><strong>推理步数</strong>：越高质量越好，但生成时间越长</li>
                        <li><strong>引导系数</strong>：控制与提示词的匹配度</li>
                        <li><strong>随机种子</strong>：相同种子会生成相似的图片</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-primary" id="closeHelp">知道了</button>
            </div>
        </div>
    </div>

    <!-- 遮罩层 -->
    <div class="overlay hidden" id="overlay"></div>

    <!-- JavaScript -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
