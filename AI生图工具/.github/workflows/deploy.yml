name: Deploy AI Image Generator to GitHub Pages

on:
  push:
    branches: [ main ]
    paths:
      - 'AI生图工具/**'
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - name: Setup Pages
      uses: actions/configure-pages@v3

    - name: Copy AI Image Generator files
      run: |
        mkdir -p ./deploy
        cp -r "./AI生图工具/"* ./deploy/

    - name: Upload artifact
      uses: actions/upload-pages-artifact@v2
      with:
        path: './deploy'

    - name: Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@v2

# 权限设置
permissions:
  contents: read
  pages: write
  id-token: write

# 并发设置
concurrency:
  group: "pages"
  cancel-in-progress: false
