<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码生成器 - 专业的二维码制作工具</title>
    <meta name="description" content="免费在线二维码生成器，支持文本、链接转二维码，可自定义颜色和样式，支持微信扫码">
    
    <!-- Tailwind CSS -->
    <script src="https://unpkg.com/@tailwindcss/browser@4"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    
    <!-- QRCode.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
    
    <style>
        /* 自定义动画和样式 */
        .smooth-transition {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .pulse-animation {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: .8;
            }
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
        }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* 焦点样式 */
        .focus-ring:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen font-sans">
    <!-- 导航栏 -->
    <nav class="glass-effect border-b border-white/20 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-qrcode text-white text-lg" aria-hidden="true"></i>
                    </div>
                    <h1 class="text-xl font-bold text-gray-800">二维码生成器</h1>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="#features" class="text-gray-600 hover:text-blue-600 smooth-transition focus-ring rounded-md px-2 py-1">功能特色</a>
                    <a href="#generator" class="text-gray-600 hover:text-blue-600 smooth-transition focus-ring rounded-md px-2 py-1">开始制作</a>
                    <a href="#help" class="text-gray-600 hover:text-blue-600 smooth-transition focus-ring rounded-md px-2 py-1">使用帮助</a>
                </div>
                <!-- 移动端菜单按钮 -->
                <button id="mobile-menu-btn" class="md:hidden p-2 rounded-md text-gray-600 hover:text-blue-600 hover:bg-white/50 smooth-transition focus-ring" aria-label="打开菜单">
                    <i class="fas fa-bars text-lg" aria-hidden="true"></i>
                </button>
            </div>
        </div>
        
        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="md:hidden hidden bg-white/95 backdrop-blur-sm border-t border-gray-200">
            <div class="px-4 py-3 space-y-2">
                <a href="#features" class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md smooth-transition">功能特色</a>
                <a href="#generator" class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md smooth-transition">开始制作</a>
                <a href="#help" class="block px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md smooth-transition">使用帮助</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 功能介绍区域 -->
        <section id="features" class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">专业的二维码生成工具</h2>
            <p class="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">支持文本、链接转二维码，可自定义颜色和样式，完美兼容微信扫码，让您的二维码更加个性化</p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div class="bg-white rounded-xl p-6 shadow-lg hover-lift smooth-transition">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-magic text-blue-600 text-xl" aria-hidden="true"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">自定义样式</h3>
                    <p class="text-gray-600">支持自定义前景色、背景色，多种样式选择，让您的二维码独一无二</p>
                </div>
                
                <div class="bg-white rounded-xl p-6 shadow-lg hover-lift smooth-transition">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-mobile-alt text-green-600 text-xl" aria-hidden="true"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">微信兼容</h3>
                    <p class="text-gray-600">完美支持微信扫码，确保生成的二维码在各种场景下都能正常使用</p>
                </div>
                
                <div class="bg-white rounded-xl p-6 shadow-lg hover-lift smooth-transition">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-download text-purple-600 text-xl" aria-hidden="true"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">高清下载</h3>
                    <p class="text-gray-600">支持高清PNG格式下载，适用于印刷和数字媒体使用</p>
                </div>
            </div>
        </section>

        <!-- 二维码生成器主体 -->
        <section id="generator" class="bg-white rounded-2xl shadow-xl p-6 md:p-8 mb-12">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- 左侧：输入和设置区域 -->
                <div class="space-y-6">
                    <div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-edit text-blue-600 mr-2" aria-hidden="true"></i>
                            输入内容
                        </h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label for="qr-input" class="block text-sm font-medium text-gray-700 mb-2">
                                    请输入文本或链接
                                </label>
                                <textarea 
                                    id="qr-input" 
                                    rows="4" 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent smooth-transition resize-none"
                                    placeholder="例如：https://www.example.com 或者任何文本内容"
                                    aria-describedby="input-help"
                                ></textarea>
                                <p id="input-help" class="mt-2 text-sm text-gray-500">
                                    <i class="fas fa-info-circle mr-1" aria-hidden="true"></i>
                                    支持网址、文本、电话号码等各种内容
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- 自定义设置 -->
                    <div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-palette text-purple-600 mr-2" aria-hidden="true"></i>
                            自定义样式
                        </h3>

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <!-- 前景色选择 -->
                            <div>
                                <label for="foreground-color" class="block text-sm font-medium text-gray-700 mb-2">
                                    前景色
                                </label>
                                <div class="flex items-center space-x-3">
                                    <input
                                        type="color"
                                        id="foreground-color"
                                        value="#000000"
                                        class="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer focus-ring"
                                        aria-label="选择前景色"
                                    >
                                    <span id="fg-color-text" class="text-sm text-gray-600 font-mono">#000000</span>
                                </div>
                            </div>

                            <!-- 背景色选择 -->
                            <div>
                                <label for="background-color" class="block text-sm font-medium text-gray-700 mb-2">
                                    背景色
                                </label>
                                <div class="flex items-center space-x-3">
                                    <input
                                        type="color"
                                        id="background-color"
                                        value="#ffffff"
                                        class="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer focus-ring"
                                        aria-label="选择背景色"
                                    >
                                    <span id="bg-color-text" class="text-sm text-gray-600 font-mono">#ffffff</span>
                                </div>
                            </div>
                        </div>

                        <!-- 二维码尺寸 -->
                        <div class="mt-4">
                            <label for="qr-size" class="block text-sm font-medium text-gray-700 mb-2">
                                二维码尺寸
                            </label>
                            <select
                                id="qr-size"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent smooth-transition"
                            >
                                <option value="200">小 (200x200)</option>
                                <option value="300" selected>中 (300x300)</option>
                                <option value="400">大 (400x400)</option>
                                <option value="500">超大 (500x500)</option>
                            </select>
                        </div>

                        <!-- 错误纠正级别 -->
                        <div class="mt-4">
                            <label for="error-level" class="block text-sm font-medium text-gray-700 mb-2">
                                错误纠正级别
                            </label>
                            <select
                                id="error-level"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent smooth-transition"
                            >
                                <option value="L">低 (L) - 约7%</option>
                                <option value="M" selected>中 (M) - 约15%</option>
                                <option value="Q">高 (Q) - 约25%</option>
                                <option value="H">最高 (H) - 约30%</option>
                            </select>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex flex-col sm:flex-row gap-3">
                        <button
                            id="generate-btn"
                            class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 smooth-transition focus-ring disabled:opacity-50 disabled:cursor-not-allowed"
                            aria-describedby="generate-help"
                        >
                            <i class="fas fa-magic mr-2" aria-hidden="true"></i>
                            生成二维码
                        </button>

                        <button
                            id="download-btn"
                            class="flex-1 bg-green-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-600 smooth-transition focus-ring disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled
                            aria-label="下载二维码图片"
                        >
                            <i class="fas fa-download mr-2" aria-hidden="true"></i>
                            下载图片
                        </button>
                    </div>
                    <p id="generate-help" class="text-sm text-gray-500">
                        <i class="fas fa-lightbulb mr-1" aria-hidden="true"></i>
                        点击生成按钮创建您的专属二维码
                    </p>
                </div>

                <!-- 右侧：二维码预览区域 -->
                <div class="flex flex-col items-center justify-center">
                    <div class="w-full max-w-md">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4 text-center flex items-center justify-center">
                            <i class="fas fa-eye text-green-600 mr-2" aria-hidden="true"></i>
                            预览效果
                        </h3>

                        <!-- 二维码显示区域 -->
                        <div id="qr-container" class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-xl p-8 text-center min-h-[300px] flex flex-col items-center justify-center smooth-transition">
                            <div id="qr-placeholder" class="text-gray-400">
                                <i class="fas fa-qrcode text-6xl mb-4 pulse-animation" aria-hidden="true"></i>
                                <p class="text-lg font-medium">二维码将在这里显示</p>
                                <p class="text-sm mt-2">请输入内容并点击生成按钮</p>
                            </div>
                            <canvas id="qr-canvas" class="hidden max-w-full h-auto rounded-lg shadow-lg"></canvas>
                        </div>

                        <!-- 二维码信息 -->
                        <div id="qr-info" class="hidden mt-4 p-4 bg-blue-50 rounded-lg">
                            <h4 class="font-medium text-blue-800 mb-2">二维码信息</h4>
                            <div class="text-sm text-blue-700 space-y-1">
                                <p><span class="font-medium">尺寸:</span> <span id="info-size">-</span></p>
                                <p><span class="font-medium">错误纠正:</span> <span id="info-error">-</span></p>
                                <p><span class="font-medium">内容长度:</span> <span id="info-length">-</span> 字符</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 使用帮助 -->
        <section id="help" class="bg-white rounded-2xl shadow-xl p-6 md:p-8">
            <h3 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-question-circle text-blue-600 mr-3" aria-hidden="true"></i>
                使用帮助
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">常见问题</h4>
                    <div class="space-y-4">
                        <div class="border-l-4 border-blue-500 pl-4">
                            <h5 class="font-medium text-gray-800">支持哪些内容类型？</h5>
                            <p class="text-gray-600 text-sm mt-1">支持网址、文本、电话号码、邮箱地址等各种内容，最大支持2953个字符。</p>
                        </div>

                        <div class="border-l-4 border-green-500 pl-4">
                            <h5 class="font-medium text-gray-800">如何选择错误纠正级别？</h5>
                            <p class="text-gray-600 text-sm mt-1">级别越高，二维码越复杂但容错性越强。一般情况下选择"中"级别即可。</p>
                        </div>

                        <div class="border-l-4 border-purple-500 pl-4">
                            <h5 class="font-medium text-gray-800">颜色搭配建议</h5>
                            <p class="text-gray-600 text-sm mt-1">确保前景色和背景色有足够的对比度，建议对比度不低于4.5:1以确保扫码成功。</p>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">使用技巧</h4>
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-blue-600 text-xs font-bold">1</span>
                            </div>
                            <p class="text-gray-600 text-sm">输入内容后会自动生成预览，无需手动点击生成按钮</p>
                        </div>

                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-green-600 text-xs font-bold">2</span>
                            </div>
                            <p class="text-gray-600 text-sm">调整颜色和尺寸后会实时更新预览效果</p>
                        </div>

                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-purple-600 text-xs font-bold">3</span>
                            </div>
                            <p class="text-gray-600 text-sm">生成的二维码支持高清下载，适用于各种用途</p>
                        </div>

                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-orange-600 text-xs font-bold">4</span>
                            </div>
                            <p class="text-gray-600 text-sm">建议在打印前先用手机测试扫码效果</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-8 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="flex items-center justify-center space-x-2 mb-4">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-qrcode text-white" aria-hidden="true"></i>
                </div>
                <span class="text-lg font-semibold">二维码生成器</span>
            </div>
            <p class="text-gray-400 text-sm">
                © 2024 二维码生成器. 免费在线工具，让二维码制作更简单
            </p>
        </div>
    </footer>

    <script>
        // DOM 元素引用
        const elements = {
            input: document.getElementById('qr-input'),
            fgColor: document.getElementById('foreground-color'),
            bgColor: document.getElementById('background-color'),
            size: document.getElementById('qr-size'),
            errorLevel: document.getElementById('error-level'),
            generateBtn: document.getElementById('generate-btn'),
            downloadBtn: document.getElementById('download-btn'),
            canvas: document.getElementById('qr-canvas'),
            placeholder: document.getElementById('qr-placeholder'),
            container: document.getElementById('qr-container'),
            info: document.getElementById('qr-info'),
            fgColorText: document.getElementById('fg-color-text'),
            bgColorText: document.getElementById('bg-color-text'),
            infoSize: document.getElementById('info-size'),
            infoError: document.getElementById('info-error'),
            infoLength: document.getElementById('info-length'),
            mobileMenuBtn: document.getElementById('mobile-menu-btn'),
            mobileMenu: document.getElementById('mobile-menu')
        };

        // 应用状态
        let currentQRData = null;

        // 初始化应用
        function initApp() {
            setupEventListeners();
            updateColorTexts();
            setupSmoothScrolling();
            setupAccessibility();
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 输入内容变化时自动生成二维码
            elements.input.addEventListener('input', debounce(generateQRCode, 300));

            // 颜色选择器变化
            elements.fgColor.addEventListener('change', () => {
                updateColorTexts();
                if (elements.input.value.trim()) generateQRCode();
            });

            elements.bgColor.addEventListener('change', () => {
                updateColorTexts();
                if (elements.input.value.trim()) generateQRCode();
            });

            // 尺寸和错误级别变化
            elements.size.addEventListener('change', () => {
                if (elements.input.value.trim()) generateQRCode();
            });

            elements.errorLevel.addEventListener('change', () => {
                if (elements.input.value.trim()) generateQRCode();
            });

            // 按钮点击事件
            elements.generateBtn.addEventListener('click', generateQRCode);
            elements.downloadBtn.addEventListener('click', downloadQRCode);

            // 移动端菜单
            elements.mobileMenuBtn.addEventListener('click', toggleMobileMenu);

            // 键盘导航支持
            document.addEventListener('keydown', handleKeyboardNavigation);
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 更新颜色文本显示
        function updateColorTexts() {
            elements.fgColorText.textContent = elements.fgColor.value.toUpperCase();
            elements.bgColorText.textContent = elements.bgColor.value.toUpperCase();
        }

        // 使用QRCode.js生成真实可扫码的二维码（稳健等待渲染完成，避免空白）
        async function generateQRCode() {
            const text = elements.input.value.trim();
            if (!text) {
                showPlaceholder();
                return;
            }
            try {
                showLoading();
                const size = parseInt(elements.size.value);
                const fgColor = elements.fgColor.value;
                const bgColor = elements.bgColor.value;
                const errorLevel = elements.errorLevel.value; // L/M/Q/H

                // 临时容器：让 qrcodejs 渲染到这里
                const tempContainer = document.createElement('div');
                tempContainer.setAttribute('aria-hidden', 'true');
                tempContainer.style.position = 'absolute';
                tempContainer.style.left = '-9999px';
                document.body.appendChild(tempContainer);

                // 生成
                // 注意：qrcodejs 会异步插入 <canvas> 或 <img>（data URL）
                // 我们随后等待节点就绪（含 <img> 的 onload）再绘制到页面 canvas
                // eslint-disable-next-line no-undef
                new QRCode(tempContainer, {
                    text,
                    width: size,
                    height: size,
                    colorDark: fgColor,
                    colorLight: bgColor,
                    // eslint-disable-next-line no-undef
                    correctLevel: QRCode.CorrectLevel[errorLevel]
                });

                // 等待 qrcodejs 产出的节点可用
                const node = await waitForQRNode(tempContainer, 3000);

                // 绘制到页面中的固定 canvas，便于统一下载
                const ctx = elements.canvas.getContext('2d');
                elements.canvas.width = size;
                elements.canvas.height = size;
                ctx.drawImage(node, 0, 0, size, size);

                // 展示与信息
                showQRCode();
                updateQRInfo(text, { width: size, height: size, errorCorrectionLevel: errorLevel });
                elements.downloadBtn.disabled = false;
                currentQRData = { text, options: { width: size, height: size, errorCorrectionLevel: errorLevel } };

                // 清理
                document.body.removeChild(tempContainer);
            } catch (error) {
                console.error('生成二维码失败:', error);
                showError('生成二维码失败，请重试或检查网络/扩展干扰');
            }
        }

        // 等待 qrcodejs 插入的 <canvas> 或 <img> 节点可绘制
        function waitForQRNode(container, timeout = 3000) {
            return new Promise((resolve, reject) => {
                const pick = () => container.querySelector('canvas,img');
                const done = (node) => resolve(node);

                let node = pick();
                if (node) {
                    if (node.tagName === 'CANVAS') return done(node);
                    if (node.tagName === 'IMG') {
                        if (node.complete) return done(node);
                        node.addEventListener('load', () => done(node), { once: true });
                    }
                }

                const obs = new MutationObserver(() => {
                    node = pick();
                    if (!node) return;
                    if (node.tagName === 'CANVAS') {
                        obs.disconnect();
                        done(node);
                    } else if (node.tagName === 'IMG') {
                        if (node.complete) {
                            obs.disconnect();
                            done(node);
                        } else {
                            node.addEventListener('load', () => {
                                obs.disconnect();
                                done(node);
                            }, { once: true });
                        }
                    }
                });
                obs.observe(container, { childList: true, subtree: true });
                setTimeout(() => {
                    obs.disconnect();
                    reject(new Error('QR generation timeout'));
                }, timeout);
            });
        }

        // 显示占位符
        function showPlaceholder() {
            elements.placeholder.classList.remove('hidden');
            elements.canvas.classList.add('hidden');
            elements.info.classList.add('hidden');
            elements.downloadBtn.disabled = true;
            elements.container.classList.remove('bg-white');
            elements.container.classList.add('bg-gray-50', 'border-dashed');
        }

        // 显示加载状态
        function showLoading() {
            elements.placeholder.innerHTML = `
                <div class="text-blue-500">
                    <i class="fas fa-spinner fa-spin text-4xl mb-4" aria-hidden="true"></i>
                    <p class="text-lg font-medium">正在生成二维码...</p>
                </div>
            `;
            elements.placeholder.classList.remove('hidden');
            elements.canvas.classList.add('hidden');
        }

        // 显示二维码
        function showQRCode() {
            elements.placeholder.classList.add('hidden');
            elements.canvas.classList.remove('hidden');
            elements.info.classList.remove('hidden');
            elements.container.classList.add('bg-white');
            elements.container.classList.remove('bg-gray-50', 'border-dashed');
        }

        // 显示错误信息
        function showError(message) {
            elements.placeholder.innerHTML = `
                <div class="text-red-500">
                    <i class="fas fa-exclamation-triangle text-4xl mb-4" aria-hidden="true"></i>
                    <p class="text-lg font-medium">生成失败</p>
                    <p class="text-sm mt-2">${message}</p>
                </div>
            `;
            elements.placeholder.classList.remove('hidden');
            elements.canvas.classList.add('hidden');
            elements.info.classList.add('hidden');
        }

        // 更新二维码信息
        function updateQRInfo(text, options) {
            const errorLevels = {
                'L': '低 (L) - 约7%',
                'M': '中 (M) - 约15%',
                'Q': '高 (Q) - 约25%',
                'H': '最高 (H) - 约30%'
            };

            elements.infoSize.textContent = `${options.width}x${options.height}`;
            elements.infoError.textContent = errorLevels[options.errorCorrectionLevel];
            elements.infoLength.textContent = text.length;
        }

        // 下载二维码
        function downloadQRCode() {
            if (!currentQRData) return;

            try {
                const link = document.createElement('a');
                link.download = `qrcode_${Date.now()}.png`;
                link.href = elements.canvas.toDataURL('image/png');

                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 显示成功提示
                showToast('二维码已下载成功！', 'success');

            } catch (error) {
                console.error('下载失败:', error);
                showToast('下载失败，请重试', 'error');
            }
        }

        // 切换移动端菜单
        function toggleMobileMenu() {
            elements.mobileMenu.classList.toggle('hidden');

            // 更新按钮图标
            const icon = elements.mobileMenuBtn.querySelector('i');
            if (elements.mobileMenu.classList.contains('hidden')) {
                icon.className = 'fas fa-bars text-lg';
                elements.mobileMenuBtn.setAttribute('aria-label', '打开菜单');
            } else {
                icon.className = 'fas fa-times text-lg';
                elements.mobileMenuBtn.setAttribute('aria-label', '关闭菜单');
            }
        }

        // 设置平滑滚动
        function setupSmoothScrolling() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });

                        // 关闭移动端菜单
                        if (!elements.mobileMenu.classList.contains('hidden')) {
                            toggleMobileMenu();
                        }
                    }
                });
            });
        }

        // 设置可访问性
        function setupAccessibility() {
            // 为颜色选择器添加键盘支持
            [elements.fgColor, elements.bgColor].forEach(colorInput => {
                colorInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        colorInput.click();
                    }
                });
            });
        }

        // 键盘导航处理
        function handleKeyboardNavigation(e) {
            // ESC 键关闭移动端菜单
            if (e.key === 'Escape' && !elements.mobileMenu.classList.contains('hidden')) {
                toggleMobileMenu();
            }

            // Ctrl+Enter 快速生成二维码
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                generateQRCode();
            }
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white font-medium smooth-transition ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' : 'bg-blue-500'
            }`;
            toast.textContent = message;

            document.body.appendChild(toast);

            // 动画显示
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
                toast.style.opacity = '1';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                toast.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initApp);
    </script>
</body>
</html>
