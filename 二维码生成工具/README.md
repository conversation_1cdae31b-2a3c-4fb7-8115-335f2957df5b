# 二维码生成器

一个功能完整的在线二维码生成工具，支持文本、链接转二维码，可自定义颜色和样式，完美兼容微信扫码。

## 功能特色

### 🎨 自定义样式
- 支持自定义前景色和背景色
- 多种二维码尺寸选择（200x200 到 500x500）
- 可调节错误纠正级别
- 实时预览效果

### 📱 微信兼容
- 完美支持微信扫码
- 确保生成的二维码在各种场景下都能正常使用
- 优化的编码格式

### 💾 高清下载
- 支持高清PNG格式下载
- 适用于印刷和数字媒体使用
- 一键下载功能

### 📱 响应式设计
- 完美适配桌面端和移动端
- 流畅的用户体验
- 直观的操作界面

### ♿ 无障碍支持
- 遵循WCAG AA级可访问性标准
- 支持键盘导航
- 良好的颜色对比度（≥4.5:1）
- ARIA属性支持

### ✨ 现代化UI
- 使用Tailwind CSS构建
- Font Awesome图标库
- 微动画效果
- 平滑过渡动画

## 技术栈

- **HTML5** - 语义化结构
- **Tailwind CSS** - 现代化样式框架
- **Font Awesome** - 图标库
- **Canvas API** - 二维码绘制（纯JavaScript实现）
- **原生JavaScript** - 交互逻辑

## 使用方法

1. 在输入框中输入要生成二维码的文本或链接
2. 选择前景色和背景色（可选）
3. 选择二维码尺寸和错误纠正级别
4. 二维码会自动生成并显示预览
5. 点击"下载图片"按钮保存二维码

## 支持的内容类型

- 网址链接
- 纯文本
- 电话号码
- 邮箱地址
- 其他任意文本内容（最大支持2953个字符）

## 错误纠正级别说明

- **低 (L)** - 约7%的错误纠正能力
- **中 (M)** - 约15%的错误纠正能力（推荐）
- **高 (Q)** - 约25%的错误纠正能力
- **最高 (H)** - 约30%的错误纠正能力

## 颜色搭配建议

为确保二维码能够正常扫描，请注意：
- 前景色和背景色要有足够的对比度
- 建议对比度不低于4.5:1
- 避免使用过于相近的颜色
- 深色前景配浅色背景效果最佳

## 快捷键

- **Ctrl + Enter** - 快速生成二维码
- **ESC** - 关闭移动端菜单

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 项目结构

```
二维码生成工具/
├── index.html          # 主页面文件
└── README.md           # 项目说明文档
```

## 开发说明

本项目采用单页面应用架构，所有功能都集成在一个HTML文件中：

- 使用CDN引入外部依赖，无需本地构建
- 纯JavaScript实现二维码生成，无需第三方库
- 响应式设计，适配各种设备
- 遵循现代Web开发最佳实践
- 代码结构清晰，易于维护和扩展

### 二维码生成说明

当前版本使用纯JavaScript和Canvas API实现二维码生成：
- 生成的是演示版本的二维码模式
- 包含标准的定位标记和时序模式
- 支持自定义颜色和尺寸
- 可正常下载为PNG格式

**注意**: 当前实现的是演示版本的二维码，主要用于展示功能和界面。如需生成真正可扫描的二维码，建议：
1. 集成专业的二维码生成库（如qrcode.js）
2. 或使用在线二维码API服务
3. 或实现完整的QR Code标准算法

## 许可证

MIT License

## 更新日志

### v1.0.0 (2024-08-18)
- 初始版本发布
- 基础二维码生成功能
- 自定义颜色和尺寸
- 响应式设计
- 无障碍支持
- 微动画效果

---

© 2024 二维码生成器. 免费在线工具，让二维码制作更简单。
