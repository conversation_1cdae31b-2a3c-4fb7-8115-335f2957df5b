<?php
/**
 * 用户认证API
 * 处理登录、注册、登出等操作
 */

define('ADMIN_ACCESS', true);
require_once '../config/config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

/**
 * 返回JSON响应
 */
function jsonResponse($data, $code = 200) {
    http_response_code($code);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 用户登录
 */
function login() {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['username']) || !isset($input['password'])) {
            jsonResponse(['error' => '用户名和密码不能为空'], 400);
        }
        
        $username = cleanInput($input['username']);
        $password = $input['password'];
        
        // 检查登录尝试次数
        $ip = getClientIP();
        $attempts = $_SESSION['login_attempts'][$ip] ?? 0;
        
        if ($attempts >= LOGIN_MAX_ATTEMPTS) {
            $lastAttempt = $_SESSION['last_attempt'][$ip] ?? 0;
            if (time() - $lastAttempt < LOGIN_LOCKOUT_TIME) {
                jsonResponse(['error' => '登录尝试次数过多，请稍后再试'], 429);
            } else {
                // 重置尝试次数
                unset($_SESSION['login_attempts'][$ip]);
                unset($_SESSION['last_attempt'][$ip]);
            }
        }
        
        $db = getDB();
        $user = $db->fetchOne(
            "SELECT id, username, email, password, is_admin, is_active FROM users WHERE username = ? OR email = ?",
            [$username, $username]
        );
        
        if (!$user || !password_verify($password, $user['password'])) {
            // 记录失败尝试
            $_SESSION['login_attempts'][$ip] = ($attempts + 1);
            $_SESSION['last_attempt'][$ip] = time();
            
            writeLog("登录失败: 用户名 $username, IP: $ip", 'WARNING');
            jsonResponse(['error' => '用户名或密码错误'], 401);
        }
        
        if (!$user['is_active']) {
            jsonResponse(['error' => '账户已被禁用'], 403);
        }
        
        // 登录成功，清除失败记录
        unset($_SESSION['login_attempts'][$ip]);
        unset($_SESSION['last_attempt'][$ip]);
        
        // 设置会话
        $_SESSION['admin_user_id'] = $user['id'];
        $_SESSION['admin_username'] = $user['username'];
        $_SESSION['admin_is_admin'] = (bool)$user['is_admin'];
        $_SESSION['admin_login_time'] = time();
        
        // 更新最后登录时间
        $db->execute(
            "UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = ?",
            [$user['id']]
        );
        
        writeLog("用户登录成功: {$user['username']}, IP: $ip", 'INFO');
        
        jsonResponse([
            'success' => true,
            'message' => '登录成功',
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'is_admin' => (bool)$user['is_admin']
            ]
        ]);
        
    } catch (Exception $e) {
        writeLog("登录异常: " . $e->getMessage(), 'ERROR');
        jsonResponse(['error' => '登录失败，请稍后再试'], 500);
    }
}

/**
 * 用户注册
 */
function register() {
    try {
        // 检查是否允许注册
        if (!getSystemConfig('enable_registration', '1')) {
            jsonResponse(['error' => '系统暂不开放注册'], 403);
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['username']) || !isset($input['email']) || !isset($input['password'])) {
            jsonResponse(['error' => '用户名、邮箱和密码不能为空'], 400);
        }
        
        $username = cleanInput($input['username']);
        $email = cleanInput($input['email']);
        $password = $input['password'];
        
        // 验证输入
        if (strlen($username) < 3 || strlen($username) > 50) {
            jsonResponse(['error' => '用户名长度必须在3-50个字符之间'], 400);
        }
        
        if (!isValidEmail($email)) {
            jsonResponse(['error' => '邮箱格式不正确'], 400);
        }
        
        if (strlen($password) < PASSWORD_MIN_LENGTH) {
            jsonResponse(['error' => "密码长度不能少于" . PASSWORD_MIN_LENGTH . "个字符"], 400);
        }
        
        $db = getDB();
        
        // 检查用户名是否已存在
        $existing = $db->fetchOne("SELECT id FROM users WHERE username = ?", [$username]);
        if ($existing) {
            jsonResponse(['error' => '用户名已存在'], 409);
        }
        
        // 检查邮箱是否已存在
        $existing = $db->fetchOne("SELECT id FROM users WHERE email = ?", [$email]);
        if ($existing) {
            jsonResponse(['error' => '邮箱已被注册'], 409);
        }
        
        // 创建用户
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $userId = $db->insert(
            "INSERT INTO users (username, email, password) VALUES (?, ?, ?)",
            [$username, $email, $hashedPassword]
        );
        
        writeLog("新用户注册: $username ($email)", 'INFO');
        
        jsonResponse([
            'success' => true,
            'message' => '注册成功',
            'user_id' => $userId
        ]);
        
    } catch (Exception $e) {
        writeLog("注册异常: " . $e->getMessage(), 'ERROR');
        jsonResponse(['error' => '注册失败，请稍后再试'], 500);
    }
}

/**
 * 用户登出
 */
function logout() {
    $username = $_SESSION['admin_username'] ?? 'unknown';
    
    // 清除会话
    session_unset();
    session_destroy();
    
    writeLog("用户登出: $username", 'INFO');
    
    jsonResponse([
        'success' => true,
        'message' => '已成功登出'
    ]);
}

/**
 * 检查登录状态
 */
function checkAuth() {
    if (isLoggedIn()) {
        $user = getCurrentUser();
        if ($user) {
            jsonResponse([
                'authenticated' => true,
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'is_admin' => (bool)$user['is_admin']
                ]
            ]);
        }
    }
    
    jsonResponse(['authenticated' => false]);
}

/**
 * 修改密码
 */
function changePassword() {
    requireLogin();
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['old_password']) || !isset($input['new_password'])) {
            jsonResponse(['error' => '旧密码和新密码不能为空'], 400);
        }
        
        $oldPassword = $input['old_password'];
        $newPassword = $input['new_password'];
        
        if (strlen($newPassword) < PASSWORD_MIN_LENGTH) {
            jsonResponse(['error' => "新密码长度不能少于" . PASSWORD_MIN_LENGTH . "个字符"], 400);
        }
        
        $db = getDB();
        $user = $db->fetchOne("SELECT password FROM users WHERE id = ?", [$_SESSION['admin_user_id']]);
        
        if (!$user || !password_verify($oldPassword, $user['password'])) {
            jsonResponse(['error' => '旧密码错误'], 401);
        }
        
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        $db->execute(
            "UPDATE users SET password = ? WHERE id = ?",
            [$hashedPassword, $_SESSION['admin_user_id']]
        );
        
        writeLog("用户修改密码: " . $_SESSION['admin_username'], 'INFO');
        
        jsonResponse([
            'success' => true,
            'message' => '密码修改成功'
        ]);
        
    } catch (Exception $e) {
        writeLog("修改密码异常: " . $e->getMessage(), 'ERROR');
        jsonResponse(['error' => '密码修改失败'], 500);
    }
}

// 路由处理
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'login':
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            jsonResponse(['error' => '只支持POST请求'], 405);
        }
        login();
        break;
        
    case 'register':
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            jsonResponse(['error' => '只支持POST请求'], 405);
        }
        register();
        break;
        
    case 'logout':
        logout();
        break;
        
    case 'check':
        checkAuth();
        break;
        
    case 'change_password':
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            jsonResponse(['error' => '只支持POST请求'], 405);
        }
        changePassword();
        break;
        
    default:
        jsonResponse(['error' => '无效的操作'], 400);
}
?>
