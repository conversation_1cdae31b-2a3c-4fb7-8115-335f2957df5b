<?php
/**
 * 对话API
 * 代理DeepSeek API调用，保护API密钥安全
 */

define('ADMIN_ACCESS', true);
require_once '../config/config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

/**
 * 返回JSON响应
 */
function jsonResponse($data, $code = 200) {
    http_response_code($code);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 获取活跃的API密钥
 */
function getActiveApiKey() {
    try {
        $db = getDB();
        $apiKey = $db->fetchOne(
            "SELECT api_key FROM api_keys WHERE is_active = 1 AND provider = 'deepseek' ORDER BY id LIMIT 1"
        );
        
        if (!$apiKey) {
            throw new Exception('没有可用的API密钥');
        }
        
        return $apiKey['api_key'];
    } catch (Exception $e) {
        writeLog("获取API密钥失败: " . $e->getMessage(), 'ERROR');
        throw new Exception('API密钥配置错误');
    }
}

/**
 * 调用DeepSeek API
 */
function callDeepSeekAPI($messages, $apiKey) {
    $data = [
        'model' => getSystemConfig('default_model', 'deepseek-chat'),
        'messages' => $messages,
        'stream' => false,
        'temperature' => (float)getSystemConfig('temperature', DEFAULT_TEMPERATURE),
        'max_tokens' => (int)getSystemConfig('max_tokens', MAX_TOKENS)
    ];
    
    $headers = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => DEEPSEEK_API_URL,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_TIMEOUT => (int)getSystemConfig('api_timeout', API_TIMEOUT),
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_USERAGENT => 'DeepSeek-Chat-Assistant/1.0'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        throw new Exception("网络请求失败: $error");
    }
    
    if ($httpCode !== 200) {
        $errorData = json_decode($response, true);
        $errorMsg = $errorData['error']['message'] ?? "API请求失败 (HTTP $httpCode)";
        throw new Exception($errorMsg);
    }
    
    $result = json_decode($response, true);
    if (!$result || !isset($result['choices'][0]['message']['content'])) {
        throw new Exception('API返回数据格式错误');
    }
    
    return $result;
}

/**
 * 保存对话记录
 */
function saveConversation($userId, $sessionId, $userMessage, $aiResponse, $tokensUsed = 0) {
    try {
        $db = getDB();
        
        // 获取或创建对话
        $conversation = $db->fetchOne(
            "SELECT id FROM conversations WHERE session_id = ? AND user_id = ?",
            [$sessionId, $userId]
        );
        
        if (!$conversation) {
            // 创建新对话
            $title = mb_substr($userMessage, 0, 50) . (mb_strlen($userMessage) > 50 ? '...' : '');
            $conversationId = $db->insert(
                "INSERT INTO conversations (user_id, title, session_id) VALUES (?, ?, ?)",
                [$userId, $title, $sessionId]
            );
        } else {
            $conversationId = $conversation['id'];
        }
        
        // 保存用户消息
        $db->insert(
            "INSERT INTO messages (conversation_id, role, content) VALUES (?, 'user', ?)",
            [$conversationId, $userMessage]
        );
        
        // 保存AI回复
        $db->insert(
            "INSERT INTO messages (conversation_id, role, content, tokens_used) VALUES (?, 'assistant', ?, ?)",
            [$conversationId, $aiResponse, $tokensUsed]
        );
        
        // 更新API密钥使用次数
        $db->execute(
            "UPDATE api_keys SET usage_count = usage_count + 1 WHERE is_active = 1 AND provider = 'deepseek' LIMIT 1"
        );
        
        return $conversationId;
        
    } catch (Exception $e) {
        writeLog("保存对话记录失败: " . $e->getMessage(), 'ERROR');
        // 不抛出异常，避免影响用户体验
    }
}

/**
 * 发送消息
 */
function sendMessage() {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['message']) || empty(trim($input['message']))) {
            jsonResponse(['error' => '消息内容不能为空'], 400);
        }
        
        $userMessage = cleanInput($input['message']);
        $sessionId = $input['session_id'] ?? 'session_' . time();
        $conversationHistory = $input['history'] ?? [];
        
        // 限制消息长度
        if (mb_strlen($userMessage) > 2000) {
            jsonResponse(['error' => '消息内容过长，请控制在2000字符以内'], 400);
        }
        
        // 构建消息历史
        $messages = [
            [
                'role' => 'system',
                'content' => '你是DeepSeek开发的AI助手，请用中文回答用户的问题。你应该友善、有帮助，并提供准确的信息。'
            ]
        ];
        
        // 添加历史消息（限制数量避免token过多）
        $historyLimit = 10; // 最多保留10轮对话
        $recentHistory = array_slice($conversationHistory, -$historyLimit);
        foreach ($recentHistory as $msg) {
            if (isset($msg['role']) && isset($msg['content'])) {
                $messages[] = [
                    'role' => $msg['role'],
                    'content' => $msg['content']
                ];
            }
        }
        
        // 添加当前用户消息
        $messages[] = [
            'role' => 'user',
            'content' => $userMessage
        ];
        
        // 获取API密钥
        $apiKey = getActiveApiKey();
        
        // 调用DeepSeek API
        $startTime = microtime(true);
        $result = callDeepSeekAPI($messages, $apiKey);
        $responseTime = round((microtime(true) - $startTime) * 1000); // 毫秒
        
        $aiResponse = $result['choices'][0]['message']['content'];
        $tokensUsed = $result['usage']['total_tokens'] ?? 0;
        
        // 保存对话记录
        $userId = $_SESSION['admin_user_id'] ?? null;
        if ($userId) {
            saveConversation($userId, $sessionId, $userMessage, $aiResponse, $tokensUsed);
        }
        
        // 记录API调用日志
        writeLog("API调用成功: 用户ID $userId, 响应时间 {$responseTime}ms, Token使用 $tokensUsed", 'INFO');
        
        jsonResponse([
            'success' => true,
            'response' => $aiResponse,
            'session_id' => $sessionId,
            'tokens_used' => $tokensUsed,
            'response_time' => $responseTime
        ]);
        
    } catch (Exception $e) {
        writeLog("API调用失败: " . $e->getMessage(), 'ERROR');
        jsonResponse(['error' => $e->getMessage()], 500);
    }
}

/**
 * 获取对话历史
 */
function getConversationHistory() {
    try {
        $sessionId = $_GET['session_id'] ?? '';
        $userId = $_SESSION['admin_user_id'] ?? null;
        
        if (!$sessionId) {
            jsonResponse(['error' => '会话ID不能为空'], 400);
        }
        
        $db = getDB();
        
        // 获取对话信息
        $conversation = $db->fetchOne(
            "SELECT id, title FROM conversations WHERE session_id = ? AND (user_id = ? OR user_id IS NULL)",
            [$sessionId, $userId]
        );
        
        if (!$conversation) {
            jsonResponse([
                'success' => true,
                'conversation' => null,
                'messages' => []
            ]);
        }
        
        // 获取消息历史
        $messages = $db->fetchAll(
            "SELECT role, content, created_at FROM messages WHERE conversation_id = ? ORDER BY created_at ASC",
            [$conversation['id']]
        );
        
        jsonResponse([
            'success' => true,
            'conversation' => $conversation,
            'messages' => $messages
        ]);
        
    } catch (Exception $e) {
        writeLog("获取对话历史失败: " . $e->getMessage(), 'ERROR');
        jsonResponse(['error' => '获取对话历史失败'], 500);
    }
}

/**
 * 获取用户的对话列表
 */
function getConversationList() {
    try {
        $userId = $_SESSION['admin_user_id'] ?? null;
        
        if (!$userId) {
            jsonResponse(['error' => '请先登录'], 401);
        }
        
        $db = getDB();
        $conversations = $db->fetchAll(
            "SELECT c.id, c.title, c.session_id, c.created_at, 
                    COUNT(m.id) as message_count,
                    MAX(m.created_at) as last_message_at
             FROM conversations c
             LEFT JOIN messages m ON c.id = m.conversation_id
             WHERE c.user_id = ? AND c.is_active = 1
             GROUP BY c.id, c.title, c.session_id, c.created_at
             ORDER BY COALESCE(MAX(m.created_at), c.created_at) DESC
             LIMIT 50",
            [$userId]
        );
        
        jsonResponse([
            'success' => true,
            'conversations' => $conversations
        ]);
        
    } catch (Exception $e) {
        writeLog("获取对话列表失败: " . $e->getMessage(), 'ERROR');
        jsonResponse(['error' => '获取对话列表失败'], 500);
    }
}

// 路由处理
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'send':
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            jsonResponse(['error' => '只支持POST请求'], 405);
        }
        sendMessage();
        break;
        
    case 'history':
        getConversationHistory();
        break;
        
    case 'list':
        getConversationList();
        break;
        
    default:
        jsonResponse(['error' => '无效的操作'], 400);
}
?>
