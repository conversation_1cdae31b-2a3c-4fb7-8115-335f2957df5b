<?php
/**
 * 数据库配置文件
 * 请根据您的服务器环境修改以下配置
 */

// 数据库配置
define('DB_HOST', 'localhost');        // 数据库主机地址
define('DB_NAME', 'deepseek_chat');    // 数据库名称
define('DB_USER', 'root');             // 数据库用户名
define('DB_PASS', '');                 // 数据库密码（请设置强密码）
define('DB_CHARSET', 'utf8mb4');       // 数据库字符集

// 数据库连接类
class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    private $pdo;

    /**
     * 获取数据库连接
     */
    public function getConnection() {
        $this->pdo = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . $this->charset
            ];
            
            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $e) {
            error_log("数据库连接失败: " . $e->getMessage());
            throw new Exception("数据库连接失败，请检查配置");
        }
        
        return $this->pdo;
    }

    /**
     * 测试数据库连接
     */
    public static function testConnection() {
        try {
            $db = new Database();
            $conn = $db->getConnection();
            return true;
        } catch(Exception $e) {
            return false;
        }
    }

    /**
     * 执行SQL查询
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch(PDOException $e) {
            error_log("SQL查询失败: " . $e->getMessage());
            throw new Exception("查询执行失败");
        }
    }

    /**
     * 获取单条记录
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    /**
     * 获取多条记录
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * 执行插入操作并返回插入的ID
     */
    public function insert($sql, $params = []) {
        $this->query($sql, $params);
        return $this->getConnection()->lastInsertId();
    }

    /**
     * 执行更新或删除操作并返回影响的行数
     */
    public function execute($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * 开始事务
     */
    public function beginTransaction() {
        return $this->getConnection()->beginTransaction();
    }

    /**
     * 提交事务
     */
    public function commit() {
        return $this->getConnection()->commit();
    }

    /**
     * 回滚事务
     */
    public function rollback() {
        return $this->getConnection()->rollback();
    }
}

// 全局数据库实例
$GLOBALS['db'] = new Database();

/**
 * 获取全局数据库实例
 */
function getDB() {
    return $GLOBALS['db'];
}

/**
 * 数据库初始化检查
 */
function checkDatabaseSetup() {
    try {
        $db = getDB();
        
        // 检查必要的表是否存在
        $tables = ['users', 'api_keys', 'conversations', 'messages', 'system_config'];
        foreach ($tables as $table) {
            $result = $db->fetchOne("SHOW TABLES LIKE ?", [$table]);
            if (!$result) {
                throw new Exception("数据表 {$table} 不存在，请先运行数据库初始化脚本");
            }
        }
        
        return true;
    } catch (Exception $e) {
        error_log("数据库检查失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取系统配置
 */
function getSystemConfig($key, $default = null) {
    try {
        $db = getDB();
        $result = $db->fetchOne("SELECT config_value FROM system_config WHERE config_key = ?", [$key]);
        return $result ? $result['config_value'] : $default;
    } catch (Exception $e) {
        return $default;
    }
}

/**
 * 设置系统配置
 */
function setSystemConfig($key, $value, $description = null) {
    try {
        $db = getDB();
        
        // 检查配置是否已存在
        $existing = $db->fetchOne("SELECT id FROM system_config WHERE config_key = ?", [$key]);
        
        if ($existing) {
            // 更新现有配置
            return $db->execute(
                "UPDATE system_config SET config_value = ?, description = COALESCE(?, description) WHERE config_key = ?",
                [$value, $description, $key]
            );
        } else {
            // 插入新配置
            return $db->insert(
                "INSERT INTO system_config (config_key, config_value, description) VALUES (?, ?, ?)",
                [$key, $value, $description]
            );
        }
    } catch (Exception $e) {
        error_log("设置系统配置失败: " . $e->getMessage());
        return false;
    }
}
?>
