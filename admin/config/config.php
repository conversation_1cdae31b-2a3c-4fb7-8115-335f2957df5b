<?php
/**
 * 系统配置文件
 * DeepSeek对话助手后台管理系统
 */

// 防止直接访问
if (!defined('ADMIN_ACCESS')) {
    die('Access denied');
}

// 引入数据库配置
require_once __DIR__ . '/database.php';

// 系统基本配置
define('SYSTEM_VERSION', '1.0.0');
define('SYSTEM_NAME', 'DeepSeek对话助手管理系统');
define('ADMIN_PATH', '/admin');

// 安全配置
define('SESSION_NAME', 'deepseek_admin_session');
define('CSRF_TOKEN_NAME', 'csrf_token');
define('PASSWORD_MIN_LENGTH', 6);
define('LOGIN_MAX_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15分钟

// API配置
define('DEEPSEEK_API_URL', 'https://api.deepseek.com/chat/completions');
define('API_TIMEOUT', 30);
define('MAX_TOKENS', 2000);
define('DEFAULT_TEMPERATURE', 0.7);

// 文件上传配置
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'txt']);

// 日志配置
define('LOG_PATH', __DIR__ . '/../logs/');
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR

// 时区设置
date_default_timezone_set('Asia/Shanghai');

/**
 * 系统初始化
 */
function initSystem() {
    // 启动会话
    if (session_status() === PHP_SESSION_NONE) {
        session_name(SESSION_NAME);
        session_start();
    }
    
    // 创建必要的目录
    $dirs = [LOG_PATH, __DIR__ . '/../uploads/'];
    foreach ($dirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
    
    // 设置错误处理
    set_error_handler('customErrorHandler');
    set_exception_handler('customExceptionHandler');
}

/**
 * 自定义错误处理
 */
function customErrorHandler($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    $error = "错误: [$severity] $message 在文件 $file 第 $line 行";
    writeLog($error, 'ERROR');
    
    // 在开发环境显示错误，生产环境隐藏
    if (isDevelopment()) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px;'>$error</div>";
    }
    
    return true;
}

/**
 * 自定义异常处理
 */
function customExceptionHandler($exception) {
    $error = "未捕获的异常: " . $exception->getMessage() . " 在文件 " . $exception->getFile() . " 第 " . $exception->getLine() . " 行";
    writeLog($error, 'ERROR');
    
    if (isDevelopment()) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px;'>$error</div>";
    } else {
        echo "系统发生错误，请稍后再试。";
    }
}

/**
 * 写入日志
 */
function writeLog($message, $level = 'INFO') {
    $logFile = LOG_PATH . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message" . PHP_EOL;
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * 检查是否为开发环境
 */
function isDevelopment() {
    return in_array($_SERVER['SERVER_NAME'] ?? '', ['localhost', '127.0.0.1', '::1']);
}

/**
 * 生成CSRF令牌
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * 验证CSRF令牌
 */
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * 安全的重定向
 */
function safeRedirect($url, $exit = true) {
    // 防止开放重定向攻击
    if (filter_var($url, FILTER_VALIDATE_URL) === false && !preg_match('/^\/[^\/]/', $url)) {
        $url = '/admin/';
    }
    
    header('Location: ' . $url);
    if ($exit) {
        exit;
    }
}

/**
 * 清理输入数据
 */
function cleanInput($data) {
    if (is_array($data)) {
        return array_map('cleanInput', $data);
    }
    
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * 验证邮箱格式
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * 生成随机字符串
 */
function generateRandomString($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * 格式化文件大小
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * 获取客户端IP地址
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ip = $_SERVER[$key];
            if (strpos($ip, ',') !== false) {
                $ip = trim(explode(',', $ip)[0]);
            }
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * 检查用户是否已登录
 */
function isLoggedIn() {
    return isset($_SESSION['admin_user_id']) && !empty($_SESSION['admin_user_id']);
}

/**
 * 检查用户是否为管理员
 */
function isAdmin() {
    return isLoggedIn() && ($_SESSION['admin_is_admin'] ?? false);
}

/**
 * 要求登录
 */
function requireLogin() {
    if (!isLoggedIn()) {
        safeRedirect('/admin/login.php');
    }
}

/**
 * 要求管理员权限
 */
function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        die('需要管理员权限');
    }
}

/**
 * 获取当前用户信息
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    try {
        $db = getDB();
        return $db->fetchOne(
            "SELECT id, username, email, is_admin, created_at FROM users WHERE id = ?",
            [$_SESSION['admin_user_id']]
        );
    } catch (Exception $e) {
        writeLog("获取用户信息失败: " . $e->getMessage(), 'ERROR');
        return null;
    }
}

// 初始化系统
initSystem();

// 检查数据库连接
if (!Database::testConnection()) {
    die('数据库连接失败，请检查配置');
}

// 检查数据库是否已初始化
if (!checkDatabaseSetup()) {
    die('数据库未初始化，请先运行 database/init.sql 脚本');
}
?>
