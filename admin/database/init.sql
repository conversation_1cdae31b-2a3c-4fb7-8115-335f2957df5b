-- DeepSeek对话助手数据库初始化脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `deepseek_chat` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `deepseek_chat`;

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `email` varchar(100) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `is_admin` tinyint(1) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- API密钥表
CREATE TABLE IF NOT EXISTS `api_keys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key_name` varchar(100) NOT NULL,
  `api_key` varchar(255) NOT NULL,
  `provider` varchar(50) DEFAULT 'deepseek',
  `is_active` tinyint(1) DEFAULT 1,
  `usage_count` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 对话表
CREATE TABLE IF NOT EXISTS `conversations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `title` varchar(255) DEFAULT '新对话',
  `session_id` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 消息表
CREATE TABLE IF NOT EXISTS `messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `conversation_id` int(11) NOT NULL,
  `role` enum('user','assistant','system') NOT NULL,
  `content` text NOT NULL,
  `tokens_used` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 系统配置表
CREATE TABLE IF NOT EXISTS `system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL UNIQUE,
  `config_value` text,
  `description` varchar(255) DEFAULT NULL,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 添加外键约束
ALTER TABLE `conversations` ADD CONSTRAINT `fk_conversations_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;
ALTER TABLE `messages` ADD CONSTRAINT `fk_messages_conversation` FOREIGN KEY (`conversation_id`) REFERENCES `conversations` (`id`) ON DELETE CASCADE;

-- 插入默认管理员用户 (用户名: admin, 密码: admin123)
INSERT INTO `users` (`username`, `email`, `password`, `is_admin`) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1);

-- 插入默认API密钥（请在部署时修改）
INSERT INTO `api_keys` (`key_name`, `api_key`, `provider`) VALUES 
('DeepSeek默认密钥', '***********************************', 'deepseek');

-- 插入默认系统配置
INSERT INTO `system_config` (`config_key`, `config_value`, `description`) VALUES 
('site_name', 'DeepSeek对话助手', '网站名称'),
('max_tokens', '2000', '最大token数量'),
('temperature', '0.7', 'AI回复温度'),
('enable_registration', '1', '是否允许用户注册'),
('default_model', 'deepseek-chat', '默认AI模型'),
('api_timeout', '30', 'API请求超时时间（秒）');

-- 创建索引优化查询性能
CREATE INDEX idx_messages_role ON messages(role);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_api_keys_active ON api_keys(is_active);

-- 创建视图方便查询
CREATE VIEW conversation_stats AS
SELECT 
    c.id,
    c.title,
    c.user_id,
    u.username,
    COUNT(m.id) as message_count,
    MAX(m.created_at) as last_message_at,
    c.created_at
FROM conversations c
LEFT JOIN users u ON c.user_id = u.id
LEFT JOIN messages m ON c.id = m.conversation_id
GROUP BY c.id, c.title, c.user_id, u.username, c.created_at;

-- 创建存储过程：清理旧对话记录
DELIMITER //
CREATE PROCEDURE CleanOldConversations(IN days_old INT)
BEGIN
    DELETE FROM conversations 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL days_old DAY)
    AND is_active = 0;
END //
DELIMITER ;

-- 插入示例数据（可选）
INSERT INTO `conversations` (`user_id`, `title`, `session_id`) VALUES 
(1, '测试对话', 'session_' || UNIX_TIMESTAMP());

INSERT INTO `messages` (`conversation_id`, `role`, `content`) VALUES 
(1, 'user', '你好，请介绍一下自己'),
(1, 'assistant', '你好！我是DeepSeek对话助手，一个基于先进AI技术的智能助手。我可以帮助您解答问题、进行对话交流、提供信息查询等服务。有什么我可以帮助您的吗？');

-- 完成初始化
SELECT '数据库初始化完成！' as message;
SELECT '默认管理员账号: admin / admin123' as admin_info;
SELECT '请及时修改默认密码和API密钥！' as security_warning;
